using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class Vehicle
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address? Address { get; set; }
        
        [Required]
        [StringLength(10)]
        public string Registration { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Make { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Model { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string? Colour { get; set; }
        
        [StringLength(4)]
        public string? Year { get; set; }
        
        [StringLength(50)]
        public string? Owner { get; set; }
        
        [StringLength(20)]
        public string? VinNumber { get; set; }
        
        public DateOnly? DatePurchased { get; set; }

        [StringLength(50)]
        public string? OwnershipType { get; set; }

        [StringLength(20)]
        public string? TyreSize { get; set; }

        public int? KerbWeight { get; set; }

        public DateOnly? NextServiceDue { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties - only entities that have foreign keys pointing to Vehicle
        public ICollection<VehicleInsurancePolicy> VehicleInsurancePolicies { get; set; } = new List<VehicleInsurancePolicy>();
        public ICollection<VehicleBreakdownPolicy> VehicleBreakdownPolicies { get; set; } = new List<VehicleBreakdownPolicy>();
        public ICollection<VehicleMot> VehicleMots { get; set; } = new List<VehicleMot>();
        public ICollection<VehicleService> VehicleServices { get; set; } = new List<VehicleService>();
        public ICollection<VehicleTax> VehicleTaxes { get; set; } = new List<VehicleTax>();
        public ICollection<VehicleWarranty> VehicleWarranties { get; set; } = new List<VehicleWarranty>();
        public ICollection<VehicleServicePlan> VehicleServicePlans { get; set; } = new List<VehicleServicePlan>();
        public ICollection<VehicleFinanceAgreement> VehicleFinanceAgreements { get; set; } = new List<VehicleFinanceAgreement>();
        public Reminder? Reminder { get; set; }
    }
}
