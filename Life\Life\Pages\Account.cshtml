﻿@page
@model Life.Pages.AccountModel
@{
	ViewData["Title"] = "Account";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Account Information</h4>
                </div>
                <div class="card-body">
                    <div class="row my-4">
                        <div class="col-sm-4">
                            <strong>Username:</strong>
                        </div>
                        <div class="col-sm-8">
							@Model.CurrentUser?.UserName
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-sm-4">
                            <strong>Account Created:</strong>
                        </div>
                        <div class="col-sm-8">
                            @Model.CurrentUser?.DateCreated.ToString("dd MMMM yyyy")
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>