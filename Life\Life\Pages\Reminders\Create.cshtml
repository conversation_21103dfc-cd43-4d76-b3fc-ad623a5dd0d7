@page
@model Life.Pages.Reminders.CreateModel
@{
    ViewData["Title"] = "Create Reminder";
}

<div class="pagetitle">
    <h1>Create Reminder</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="/Dashboard">Home</a></li>
            <li class="breadcrumb-item"><a asp-page="./Index">Reminders</a></li>
            <li class="breadcrumb-item active">Create</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">New Custom Reminder</h5>

                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="row mb-3">
                            <label asp-for="Reminder.Name" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Reminder.Name" class="form-control" />
                                <span asp-validation-for="Reminder.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Reminder.ReminderDate" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Reminder.ReminderDate" class="form-control" type="date" />
                                <span asp-validation-for="Reminder.ReminderDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Reminder.Number" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Reminder.Number" class="form-control" type="number" min="1" />
                                <span asp-validation-for="Reminder.Number" class="text-danger"></span>
                                <div class="form-text">Number of days before the reminder date to be notified</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Create Reminder
                                </button>
                                <a asp-page="./Index" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Back to List
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Information</h5>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Custom Reminders</strong><br>
                        This form creates custom reminders that you can edit and delete. 
                        Automatic reminders are created by the system and cannot be modified.
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Reminder Number</strong><br>
                        The number represents how many days before the reminder date you want to be notified.
                        For example, entering "7" means you'll be reminded 7 days before the date.
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
