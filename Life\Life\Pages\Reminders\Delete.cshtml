@page
@model Life.Pages.Reminders.DeleteModel
@{
    ViewData["Title"] = "Delete Reminder";
}

<div class="pagetitle">
    <h1>Delete Reminder</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="/Dashboard">Home</a></li>
            <li class="breadcrumb-item"><a asp-page="./Index">Reminders</a></li>
            <li class="breadcrumb-item active">Delete</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-danger">
                        <i class="bi bi-exclamation-triangle"></i> Confirm Deletion
                    </h5>

                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning!</strong> Are you sure you want to delete this reminder? This action cannot be undone.
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Name:</strong></div>
                        <div class="col-sm-9">@Html.DisplayFor(model => model.Reminder.Name)</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Reminder Date:</strong></div>
                        <div class="col-sm-9">@Html.DisplayFor(model => model.Reminder.ReminderDate)</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Number:</strong></div>
                        <div class="col-sm-9">@Html.DisplayFor(model => model.Reminder.Number) days</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Type:</strong></div>
                        <div class="col-sm-9">
                            <span class="badge bg-success">Custom</span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Created:</strong></div>
                        <div class="col-sm-9">@Model.Reminder.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>

                    @if (Model.Reminder.UpdatedAt != Model.Reminder.CreatedAt)
                    {
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Last Updated:</strong></div>
                            <div class="col-sm-9">@Model.Reminder.UpdatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                    }

                    <form method="post">
                        <input type="hidden" asp-for="Reminder.Id" />
                        <div class="row">
                            <div class="col-sm-12">
                                <button type="submit" class="btn btn-danger">
                                    <i class="bi bi-trash"></i> Yes, Delete This Reminder
                                </button>
                                <a asp-page="./Index" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Cancel
                                </a>
                                <a asp-page="./Details" asp-route-id="@Model.Reminder?.Id" class="btn btn-info">
                                    <i class="bi bi-eye"></i> View Details
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Deletion Information</h5>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-info-circle"></i>
                        <strong>Custom Reminders Only</strong><br>
                        Only custom reminders can be deleted. Automatic reminders are managed by the system and cannot be removed manually.
                    </div>
                    
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Permanent Action</strong><br>
                        Once deleted, this reminder cannot be recovered. Make sure you really want to remove it.
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
