using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Reminders
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public Reminder Reminder { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Login");
            }

            var reminder = await _context.Reminders
                .Where(r => r.Id == id && r.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (reminder == null)
            {
                return NotFound();
            }

            // Only allow deletion of custom reminders
            if (reminder.IsAutomatic)
            {
                return RedirectToPage("./Details", new { id = reminder.Id });
            }

            Reminder = reminder;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Login");
            }

            var reminder = await _context.Reminders
                .Where(r => r.Id == id && r.UserId == currentUser.Id && !r.IsAutomatic)
                .FirstOrDefaultAsync();

            if (reminder == null)
            {
                return NotFound();
            }

            _context.Reminders.Remove(reminder);
            await _context.SaveChangesAsync();

            return RedirectToPage("./Index");
        }
    }
}
