@page
@model Life.Pages.Reminders.DetailsModel
@{
    ViewData["Title"] = "Reminder Details";
}

<div class="pagetitle">
    <h1>Reminder Details</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="/Dashboard">Home</a></li>
            <li class="breadcrumb-item"><a asp-page="./Index">Reminders</a></li>
            <li class="breadcrumb-item active">Details</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title">@Html.DisplayFor(model => model.Reminder.Name)</h5>
                        <div>
                            @if (Model.Reminder.IsAutomatic)
                            {
                                <span class="badge bg-info">Automatic</span>
                            }
                            else
                            {
                                <span class="badge bg-success">Custom</span>
                            }
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Reminder Date:</strong></div>
                        <div class="col-sm-9">
                            <span class="fs-5">@Model.Reminder.ReminderDate.ToString("dddd, dd MMMM yyyy")</span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Notification Days:</strong></div>
                        <div class="col-sm-9">
                            @Model.Reminder.Number days before the reminder date
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Related Item:</strong></div>
                        <div class="col-sm-9">
                            @{
                                var relatedItemLink = Model.GetRelatedItemLink(Model.Reminder);
                                var relatedItemName = Model.GetRelatedItemName(Model.Reminder);
                            }
                            @if (!string.IsNullOrEmpty(relatedItemLink))
                            {
                                <a href="@relatedItemLink" class="text-decoration-none">@relatedItemName</a>
                            }
                            else
                            {
                                @relatedItemName
                            }
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Created:</strong></div>
                        <div class="col-sm-9">@Model.Reminder.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>

                    @if (Model.Reminder.UpdatedAt != Model.Reminder.CreatedAt)
                    {
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Last Updated:</strong></div>
                            <div class="col-sm-9">@Model.Reminder.UpdatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                    }

                    <div class="row">
                        <div class="col-sm-12">
                            @if (!Model.Reminder.IsAutomatic)
                            {
                                <a asp-page="./Edit" asp-route-id="@Model.Reminder.Id" class="btn btn-primary">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a asp-page="./Delete" asp-route-id="@Model.Reminder.Id" class="btn btn-danger">
                                    <i class="bi bi-trash"></i> Delete
                                </a>
                            }
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Reminder Information</h5>
                    
                    @if (Model.Reminder.IsAutomatic)
                    {
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Automatic Reminder</strong><br>
                            This reminder was created automatically by the system and cannot be edited or deleted manually.
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            <strong>Custom Reminder</strong><br>
                            This is a custom reminder that you created. You can edit or delete it as needed.
                        </div>
                    }
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-calendar-event"></i>
                        <strong>Notification Schedule</strong><br>
                        You will be notified @Model.Reminder.Number day(s) before @Model.Reminder.ReminderDate.ToString("dd/MM/yyyy").
                        @{
                            var notificationDate = Model.Reminder.ReminderDate.AddDays(-Model.Reminder.Number);
                        }
                        <br><small>Notification date: @notificationDate.ToString("dd/MM/yyyy")</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
