using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Reminders
{
    public class DetailsModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DetailsModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public Reminder Reminder { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Login");
            }

            var reminder = await _context.Reminders
                .Where(r => r.Id == id && r.UserId == currentUser.Id)
                .Include(r => r.GeneralItem)
                .Include(r => r.DrivingLicence)
                .Include(r => r.Passport)
                .Include(r => r.VehicleTax)
                .Include(r => r.VehicleInsurancePolicy)
                .Include(r => r.VehicleMot)
                .Include(r => r.Vehicle)
                .Include(r => r.VehicleBreakdownPolicy)
                .Include(r => r.AddressInsurancePolicy)
                .Include(r => r.Tenancy)
                .Include(r => r.UtilityBill)
                .Include(r => r.Mortgage)
                .Include(r => r.CreditCard)
                .Include(r => r.Loan)
                .Include(r => r.LifeInsurancePolicy)
                .Include(r => r.DebitCard)
                .Include(r => r.RecurringPayment)
                .Include(r => r.TravelInsurancePolicy)
                .Include(r => r.GadgetInsurancePolicy)
                .Include(r => r.GlobalHealthInsuranceCard)
                .Include(r => r.EyeTest)
                .Include(r => r.VehicleWarranty)
                .Include(r => r.VehicleServicePlan)
                .Include(r => r.VehicleFinanceAgreement)
                .Include(r => r.SavingsAccount)
                .Include(r => r.SavingsAccountPot)
                .FirstOrDefaultAsync();

            if (reminder == null)
            {
                return NotFound();
            }

            Reminder = reminder;
            return Page();
        }

        public string GetRelatedItemName(Reminder reminder)
        {
            if (reminder.GeneralItem != null) return reminder.GeneralItem.Name;
            if (reminder.DrivingLicence != null) return reminder.DrivingLicence.Name;
            if (reminder.Passport != null) return reminder.Passport.Name;
            if (reminder.VehicleTax != null) return $"Vehicle Tax - {reminder.VehicleTax.Vehicle?.Registration}";
            if (reminder.VehicleInsurancePolicy != null) return reminder.VehicleInsurancePolicy.Name;
            if (reminder.VehicleMot != null) return $"MOT - {reminder.VehicleMot.Vehicle?.Registration}";
            if (reminder.Vehicle != null) return $"{reminder.Vehicle.Make} {reminder.Vehicle.Model} ({reminder.Vehicle.Registration})";
            if (reminder.VehicleBreakdownPolicy != null) return reminder.VehicleBreakdownPolicy.Name;
            if (reminder.AddressInsurancePolicy != null) return reminder.AddressInsurancePolicy.Name;
            if (reminder.Tenancy != null) return reminder.Tenancy.Name;
            if (reminder.UtilityBill != null) return reminder.UtilityBill.Name;
            if (reminder.Mortgage != null) return reminder.Mortgage.Name;
            if (reminder.CreditCard != null) return reminder.CreditCard.Name;
            if (reminder.Loan != null) return reminder.Loan.Name;
            if (reminder.LifeInsurancePolicy != null) return $"Life Insurance - {reminder.LifeInsurancePolicy.PolicyNumber}";
            if (reminder.DebitCard != null) return reminder.DebitCard.Name;
            if (reminder.RecurringPayment != null) return reminder.RecurringPayment.Name;
            if (reminder.TravelInsurancePolicy != null) return reminder.TravelInsurancePolicy.Name;
            if (reminder.GadgetInsurancePolicy != null) return reminder.GadgetInsurancePolicy.Name;
            if (reminder.GlobalHealthInsuranceCard != null) return reminder.GlobalHealthInsuranceCard.Name;
            if (reminder.EyeTest != null) return $"Eye Test - {reminder.EyeTest.Provider}";
            if (reminder.VehicleWarranty != null) return reminder.VehicleWarranty.Name;
            if (reminder.VehicleServicePlan != null) return reminder.VehicleServicePlan.Name;
            if (reminder.VehicleFinanceAgreement != null) return reminder.VehicleFinanceAgreement.Name;
            if (reminder.SavingsAccount != null) return reminder.SavingsAccount.Name;
            if (reminder.SavingsAccountPot != null) return reminder.SavingsAccountPot.Name;

            return "Custom Reminder";
        }

        public string GetRelatedItemLink(Reminder reminder)
        {
            if (reminder.GeneralItem != null) return $"/GeneralItems/Details/{reminder.GeneralItem.Id}";
            if (reminder.DrivingLicence != null) return $"/DrivingLicences/Details/{reminder.DrivingLicence.Id}";
            if (reminder.Passport != null) return $"/Passports/Details/{reminder.Passport.Id}";
            if (reminder.VehicleTax != null) return $"/VehicleTaxes/Details/{reminder.VehicleTax.Id}";
            if (reminder.VehicleInsurancePolicy != null) return $"/VehicleInsurancePolicies/Details/{reminder.VehicleInsurancePolicy.Id}";
            if (reminder.VehicleMot != null) return $"/VehicleMots/Details/{reminder.VehicleMot.Id}";
            if (reminder.Vehicle != null) return $"/Vehicles/Details/{reminder.Vehicle.Id}";
            if (reminder.VehicleBreakdownPolicy != null) return $"/VehicleBreakdownPolicies/Details/{reminder.VehicleBreakdownPolicy.Id}";
            if (reminder.AddressInsurancePolicy != null) return $"/AddressInsurancePolicies/Details/{reminder.AddressInsurancePolicy.Id}";
            if (reminder.Tenancy != null) return $"/Tenancies/Details/{reminder.Tenancy.Id}";
            if (reminder.UtilityBill != null) return $"/UtilityBills/Details/{reminder.UtilityBill.Id}";
            if (reminder.Mortgage != null) return $"/Mortgages/Details/{reminder.Mortgage.Id}";
            if (reminder.CreditCard != null) return $"/CreditCards/Details/{reminder.CreditCard.Id}";
            if (reminder.Loan != null) return $"/Loans/Details/{reminder.Loan.Id}";
            if (reminder.LifeInsurancePolicy != null) return $"/LifeInsurancePolicies/Details/{reminder.LifeInsurancePolicy.Id}";
            if (reminder.DebitCard != null) return $"/DebitCards/Details/{reminder.DebitCard.Id}";
            if (reminder.RecurringPayment != null) return $"/RecurringPayments/Details/{reminder.RecurringPayment.Id}";
            if (reminder.TravelInsurancePolicy != null) return $"/TravelInsurancePolicies/Details/{reminder.TravelInsurancePolicy.Id}";
            if (reminder.GadgetInsurancePolicy != null) return $"/GadgetInsurancePolicies/Details/{reminder.GadgetInsurancePolicy.Id}";
            if (reminder.GlobalHealthInsuranceCard != null) return $"/GlobalHealthInsuranceCards/Details/{reminder.GlobalHealthInsuranceCard.Id}";
            if (reminder.EyeTest != null) return $"/EyeTests/Details/{reminder.EyeTest.Id}";
            if (reminder.VehicleWarranty != null) return $"/VehicleWarranties/Details/{reminder.VehicleWarranty.Id}";
            if (reminder.VehicleServicePlan != null) return $"/VehicleServicePlans/Details/{reminder.VehicleServicePlan.Id}";
            if (reminder.VehicleFinanceAgreement != null) return $"/VehicleFinanceAgreements/Details/{reminder.VehicleFinanceAgreement.Id}";
            if (reminder.SavingsAccount != null) return $"/SavingsAccounts/Details/{reminder.SavingsAccount.Id}";
            if (reminder.SavingsAccountPot != null) return $"/SavingsAccountPots/Details/{reminder.SavingsAccountPot.Id}";

            return null;
        }
    }
}
