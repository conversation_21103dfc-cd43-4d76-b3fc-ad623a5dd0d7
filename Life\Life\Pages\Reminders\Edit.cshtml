@page
@model Life.Pages.Reminders.EditModel
@{
    ViewData["Title"] = "Edit Reminder";
}

<div class="pagetitle">
    <h1>Edit Reminder</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="/Dashboard">Home</a></li>
            <li class="breadcrumb-item"><a asp-page="./Index">Reminders</a></li>
            <li class="breadcrumb-item active">Edit</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Edit Custom Reminder</h5>

                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="Reminder.Id" />
                        <input type="hidden" asp-for="Reminder.UserId" />
                        <input type="hidden" asp-for="Reminder.IsAutomatic" />
                        <input type="hidden" asp-for="Reminder.CreatedAt" />
                        
                        <div class="row mb-3">
                            <label asp-for="Reminder.Name" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Reminder.Name" class="form-control" />
                                <span asp-validation-for="Reminder.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Reminder.ReminderDate" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Reminder.ReminderDate" class="form-control" type="date" />
                                <span asp-validation-for="Reminder.ReminderDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Reminder.Number" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Reminder.Number" class="form-control" type="number" min="1" />
                                <span asp-validation-for="Reminder.Number" class="text-danger"></span>
                                <div class="form-text">Number of days before the reminder date to be notified</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Save Changes
                                </button>
                                <a asp-page="./Index" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Back to List
                                </a>
                                <a asp-page="./Details" asp-route-id="@Model.Reminder?.Id" class="btn btn-info">
                                    <i class="bi bi-eye"></i> View Details
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Reminder Information</h5>
                    
                    <div class="row mb-2">
                        <div class="col-sm-4"><strong>Type:</strong></div>
                        <div class="col-sm-8">
                            <span class="badge bg-success">Custom</span>
                        </div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-sm-4"><strong>Created:</strong></div>
                        <div class="col-sm-8">@Model.Reminder?.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>
                    
                    @if (Model.Reminder?.UpdatedAt != Model.Reminder?.CreatedAt)
                    {
                        <div class="row mb-2">
                            <div class="col-sm-4"><strong>Last Updated:</strong></div>
                            <div class="col-sm-8">@Model.Reminder?.UpdatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                    }
                    
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle"></i>
                        <strong>Custom Reminders</strong><br>
                        Only custom reminders can be edited. Automatic reminders are managed by the system.
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
