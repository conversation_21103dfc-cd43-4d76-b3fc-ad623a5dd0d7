using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Reminders
{
    public class EditModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public EditModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public Reminder Reminder { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Login");
            }

            var reminder = await _context.Reminders
                .Where(r => r.Id == id && r.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (reminder == null)
            {
                return NotFound();
            }

            // Only allow editing of custom reminders
            if (reminder.IsAutomatic)
            {
                return RedirectToPage("./Details", new { id = reminder.Id });
            }

            Reminder = reminder;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Login");
            }

            // Verify the reminder belongs to the current user and is not automatic
            var existingReminder = await _context.Reminders
                .Where(r => r.Id == Reminder.Id && r.UserId == currentUser.Id && !r.IsAutomatic)
                .FirstOrDefaultAsync();

            if (existingReminder == null)
            {
                return NotFound();
            }

            // Update the fields that can be changed
            existingReminder.Name = Reminder.Name;
            existingReminder.ReminderDate = Reminder.ReminderDate;
            existingReminder.Number = Reminder.Number;
            existingReminder.UpdatedAt = DateTime.UtcNow; // Update the timestamp

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ReminderExists(Reminder.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return RedirectToPage("./Index");
        }

        private bool ReminderExists(int id)
        {
            return _context.Reminders.Any(e => e.Id == id);
        }
    }
}
