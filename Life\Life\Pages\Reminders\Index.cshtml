@page
@model Life.Pages.Reminders.IndexModel
@{
    ViewData["Title"] = "Reminders";
}

<div class="pagetitle">
    <h1>Reminders</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="/Dashboard">Home</a></li>
            <li class="breadcrumb-item active">Reminders</li>
        </ol>
    </nav>
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title">All Reminders</h5>
                        <a asp-page="./Create" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add New Reminder
                        </a>
                    </div>

                    @if (Model.Reminders.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Reminder Date</th>
                                        <th>Number</th>
                                        <th>Type</th>
                                        <th>Related Item</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var reminder in Model.Reminders)
                                    {
                                        <tr>
                                            <td>@reminder.Name</td>
                                            <td>@reminder.ReminderDate.ToString("dd/MM/yyyy")</td>
                                            <td>@reminder.Number</td>
                                            <td>
                                                @if (reminder.IsAutomatic)
                                                {
                                                    <span class="badge bg-info">Automatic</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">Custom</span>
                                                }
                                            </td>
                                            <td>
                                                @{
                                                    var relatedItemLink = Model.GetRelatedItemLink(reminder);
                                                    var relatedItemName = Model.GetRelatedItemName(reminder);
                                                }
                                                @if (!string.IsNullOrEmpty(relatedItemLink))
                                                {
                                                    <a href="@relatedItemLink" class="text-decoration-none">@relatedItemName</a>
                                                }
                                                else
                                                {
                                                    @relatedItemName
                                                }
                                            </td>
                                            <td>@reminder.CreatedAt.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="./Details" asp-route-id="@reminder.Id" class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    @if (!reminder.IsAutomatic)
                                                    {
                                                        <a asp-page="./Edit" asp-route-id="@reminder.Id" class="btn btn-sm btn-outline-secondary">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <a asp-page="./Delete" asp-route-id="@reminder.Id" class="btn btn-sm btn-outline-danger">
                                                            <i class="bi bi-trash"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            No reminders found. <a asp-page="./Create">Create your first reminder</a>.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</section>
