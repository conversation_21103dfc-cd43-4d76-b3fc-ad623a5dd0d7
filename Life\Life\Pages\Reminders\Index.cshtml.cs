using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Reminders
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public IndexModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IList<Reminder> Reminders { get; set; } = default!;

        public async Task OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser != null)
            {
                Reminders = await _context.Reminders
                    .Where(r => r.UserId == currentUser.Id)
                    .Include(r => r.GeneralItem)
                    .Include(r => r.DrivingLicence)
                    .Include(r => r.Passport)
                    .Include(r => r.VehicleTax)
                    .Include(r => r.VehicleInsurancePolicy)
                    .Include(r => r.VehicleMot)
                    .Include(r => r.Vehicle)
                    .Include(r => r.VehicleBreakdownPolicy)
                    .Include(r => r.AddressInsurancePolicy)
                    .Include(r => r.Tenancy)
                    .Include(r => r.UtilityBill)
                    .Include(r => r.Mortgage)
                    .Include(r => r.CreditCard)
                    .Include(r => r.Loan)
                    .Include(r => r.LifeInsurancePolicy)
                    .Include(r => r.DebitCard)
                    .Include(r => r.RecurringPayment)
                    .Include(r => r.TravelInsurancePolicy)
                    .Include(r => r.GadgetInsurancePolicy)
                    .Include(r => r.GlobalHealthInsuranceCard)
                    .Include(r => r.EyeTest)
                    .Include(r => r.VehicleWarranty)
                    .Include(r => r.VehicleServicePlan)
                    .Include(r => r.VehicleFinanceAgreement)
                    .Include(r => r.SavingsAccount)
                    .Include(r => r.SavingsAccountPot)
                    .OrderBy(r => r.ReminderDate)
                    .ToListAsync();
            }
        }

        public string GetRelatedItemName(Reminder reminder)
        {
            if (reminder.GeneralItem != null) return $"General Item: {reminder.GeneralItem.Name}";
            if (reminder.DrivingLicence != null) return $"Driving Licence: {reminder.DrivingLicence.LicenceNumber}";
            if (reminder.Passport != null) return $"Passport: {reminder.Passport.PassportNumber}";
            if (reminder.VehicleTax != null) return "Vehicle Tax";
            if (reminder.VehicleInsurancePolicy != null) return $"Vehicle Insurance: {reminder.VehicleInsurancePolicy.PolicyNumber}";
            if (reminder.VehicleMot != null) return "Vehicle MOT";
            if (reminder.Vehicle != null) return $"Vehicle: {reminder.Vehicle.Registration}";
            if (reminder.VehicleBreakdownPolicy != null) return $"Breakdown Policy: {reminder.VehicleBreakdownPolicy.PolicyNumber}";
            if (reminder.AddressInsurancePolicy != null) return $"Home Insurance: {reminder.AddressInsurancePolicy.PolicyNumber}";
            if (reminder.Tenancy != null) return "Tenancy";
            if (reminder.UtilityBill != null) return $"Utility Bill: {reminder.UtilityBill.Provider}";
            if (reminder.Mortgage != null) return $"Mortgage: {reminder.Mortgage.LenderName}";
            if (reminder.CreditCard != null) return $"Credit Card: {reminder.CreditCard.CardName}";
            if (reminder.Loan != null) return $"Loan: {reminder.Loan.LenderName}";
            if (reminder.LifeInsurancePolicy != null) return $"Life Insurance: {reminder.LifeInsurancePolicy.PolicyNumber}";
            if (reminder.DebitCard != null) return $"Debit Card: {reminder.DebitCard.CardName}";
            if (reminder.RecurringPayment != null) return $"Recurring Payment: {reminder.RecurringPayment.PayeeName}";
            if (reminder.TravelInsurancePolicy != null) return $"Travel Insurance: {reminder.TravelInsurancePolicy.PolicyNumber}";
            if (reminder.GadgetInsurancePolicy != null) return $"Gadget Insurance: {reminder.GadgetInsurancePolicy.PolicyNumber}";
            if (reminder.GlobalHealthInsuranceCard != null) return "GHIC";
            if (reminder.EyeTest != null) return "Eye Test";
            if (reminder.VehicleWarranty != null) return "Vehicle Warranty";
            if (reminder.VehicleServicePlan != null) return "Vehicle Service Plan";
            if (reminder.VehicleFinanceAgreement != null) return "Vehicle Finance";
            if (reminder.SavingsAccount != null) return $"Savings Account: {reminder.SavingsAccount.AccountName}";
            if (reminder.SavingsAccountPot != null) return $"Savings Pot: {reminder.SavingsAccountPot.PotName}";
            
            return "Custom Reminder";
        }
    }
}
