<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <!-- Favicons -->
    <link href="~/logo.png" rel="icon">
    <!-- Google Fonts -->
    <link href="https://fonts.gstatic.com" rel="preconnect">
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Nunito:300,300i,400,400i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.min.css">
    <!-- App CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/bootstrapoverrides.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/header.css" asp-append-version="true" />
	<link rel="stylesheet" href="~/css/main.css" asp-append-version="true" />
	<link rel="stylesheet" href="~/css/sidebar.css" asp-append-version="true" />
	<link rel="stylesheet" href="~/css/dashboard.css" asp-append-version="true" />
    <!-- ASP.NET Bundled File -->
    <link rel="stylesheet" href="~/Life.styles.css" asp-append-version="true" />
</head>
<body>
    @if (User.Identity?.IsAuthenticated == true)
    {
        <!-- Header for authenticated users -->
        <header id="header" class="header fixed-top d-flex align-items-center header-scrolled">
            <div class="d-flex align-items-center justify-content-between">
                <a href="~/" class="logo d-flex align-items-center">
                    <img src="~/logo.png" alt="">
                </a>
                <i class="bi bi-list toggle-sidebar-btn"></i>
            </div>
        </header>

        <!-- Sidebar for authenticated users -->
        <aside id="sidebar" class="sidebar">
            <ul class="sidebar-nav" id="sidebar-nav">
                <li class="nav-heading-date">@DateTime.Now.ToString("dddd, dd MMMM yyyy")</li>

                <li class="nav-item">
                    <a class="nav-link collapsed" asp-page="/Dashboard">
                        <i class="bi bi-grid"></i>
                        <span>Dashboard</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" asp-page="/Reminders">
                        <i class="bi bi-calendar3"></i>
                        <span>Reminders</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-target="#househome-nav" data-bs-toggle="collapse" href="">
                        <i class="bi bi-house-door"></i><span>House & Home</span><i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <ul id="househome-nav" class="nav-content collapse " data-bs-parent="#sidebar-nav">
                        <li>
                            <a asp-page="/Addresses">
                                <i class="bi bi-circle"></i><span>Addresses</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/UtilityBills">
                                <i class="bi bi-circle"></i><span>Utility Bills</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/Mortgages">
                                <i class="bi bi-circle"></i><span>Mortgages</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/AddressInsurancePolicies">
                                <i class="bi bi-circle"></i><span>Home Insurance</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/Tenancies">
                                <i class="bi bi-circle"></i><span>Tenancies</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-target="#motoring-nav" data-bs-toggle="collapse" href="">
                        <i class="bi bi-car-front"></i><span>Motoring</span><i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <ul id="motoring-nav" class="nav-content collapse " data-bs-parent="#sidebar-nav">
                        <li>
                            <a asp-page="/DrivingLicences">
                                <i class="bi bi-circle"></i><span>Driving Licences</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/Vehicles">
                                <i class="bi bi-circle"></i><span>Vehicles</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/VehicleInsurancePolicies">
                                <i class="bi bi-circle"></i><span>Motor Insurance</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/VehicleBreakdownPolicies">
                                <i class="bi bi-circle"></i><span>Breakdown Cover</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/VehicleWarranties">
                                <i class="bi bi-circle"></i><span>Warranties</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/VehicleFinanceAgreements">
                                <i class="bi bi-circle"></i><span>Vehicle Finance</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-target="#finances-nav" data-bs-toggle="collapse" href="">
                        <i class="bi bi-currency-pound"></i><span>Finances</span><i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <ul id="finances-nav" class="nav-content collapse " data-bs-parent="#sidebar-nav">
                        <li>
                            <a asp-page="/CurrentAccounts">
                                <i class="bi bi-circle"></i><span>Current Accounts</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/DebitCards">
                                <i class="bi bi-circle"></i><span>Debit Cards</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/DirectDebits">
                                <i class="bi bi-circle"></i><span>Direct Debits</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/StandingOrders">
                                <i class="bi bi-circle"></i><span>Standing Orders</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/RecurringPayments">
                                <i class="bi bi-circle"></i><span>Recurring Payments</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/CreditCards">
                                <i class="bi bi-circle"></i><span>Credit Cards</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/Loans">
                                <i class="bi bi-circle"></i><span>Personal Loans</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/SavingsAccounts">
                                <i class="bi bi-circle"></i><span>Savings Accounts</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/Pensions">
                                <i class="bi bi-circle"></i><span>Pensions</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/TaxableInterestPayments">
                                <i class="bi bi-circle"></i><span>Interest Payments</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-target="#health-nav" data-bs-toggle="collapse" href="">
                        <i class="bi bi-prescription2"></i><span>Health</span><i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <ul id="health-nav" class="nav-content collapse " data-bs-parent="#sidebar-nav">
                        <li>
                            <a asp-page="/GpPractices">
                                <i class="bi bi-circle"></i><span>GP Details</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/Dentists">
                                <i class="bi bi-circle"></i><span>Dentists</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/EyeTests">
                                <i class="bi bi-circle"></i><span>Eye Tests</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/LifeInsurancePolicies">
                                <i class="bi bi-circle"></i><span>Life Insurance</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-target="#travel-nav" data-bs-toggle="collapse" href="">
                        <i class="bi bi-airplane"></i><span>Travel </span><i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <ul id="travel-nav" class="nav-content collapse " data-bs-parent="#sidebar-nav">
                        <li>
                            <a asp-page="/Passports">
                                <i class="bi bi-circle"></i><span>Passports</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/TravelInsurancePolicies">
                                <i class="bi bi-circle"></i><span>Travel Insurance</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/GlobalHealthInsuranceCards">
                                <i class="bi bi-circle"></i><span>GHICs</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-target="#misc-nav" data-bs-toggle="collapse" href="">
                        <i class="bi bi-archive"></i><span>Miscellaneous </span><i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <ul id="misc-nav" class="nav-content collapse " data-bs-parent="#sidebar-nav">
                        <li>
                            <a asp-page="/GeneralItems">
                                <i class="bi bi-circle"></i><span>General Items</span>
                            </a>
                        </li>
                        <li>
                            <a asp-page="/GadgetInsurancePolicies">
                                <i class="bi bi-circle"></i><span>Gadget Insurance</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-heading">Settings</li>

                <li class="nav-item">
                    <a class="nav-link collapsed" asp-page="/Account">
                        <i class="bi bi-person"></i>
                        <span>Account</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed logout" asp-page="/Logout">
                        <i class="bi bi-box-arrow-right"></i>
                        <span>Logout</span>
                    </a>
                </li>
            </ul>
        </aside>

        <!-- Main content for authenticated users -->
        <main id="main" class="main">
            @RenderBody()
        </main>
    }
    else
    {
        <!-- Simple layout for non-authenticated users -->
        <main>
            @RenderBody()
        </main>
    }

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>