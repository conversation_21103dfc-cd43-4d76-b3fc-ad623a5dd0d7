{"GlobalPropertiesHash": "oqinQeCdC4Cgvh6hQQBk+DOpNQuSuT1VnAGJkRecfHA=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["FFyQVzNDXWVjh6jZpDY+UmTEGVYiOOOD5wCK2EW+yaI=", "zkMc9rKMhZiF4aoG3ftguswyJpJosO4kjNbqsZn6Dmc=", "k5E0ekzSQWdcj1iDpguXnJhtIoxjtT8RXTQ7b/39GQM=", "LP03SHOk9ZloH+CwePzEd2w6M44X7kEPnEasUsNNAWc=", "y0B/wI1Z17U36xgsLFtJtPwRY4t+cD6xDtW3d39uLRk=", "97O3O/YMZJlAePwpfyaFgkvbNhyeLTyRlw6gq8ZyhSA=", "3l1Dymww9T3FVLbP2dz/UYPyFAdJpK0jBqq/pFYLv/M=", "fFsHcQ64wAXnKtcB1+xNn2iOoIfGQpwbo0A0eP8CHmo=", "OxhAyDFMX0BuaYQbLFH9mkFmZuUMtla+OJ8Jq1ElqcY=", "9XAKKBilL4toU1hPWY4wHvAzttC8St2yMTXlsQE3gzs=", "lQOLPha5CkxLaM5ciXMbCIia7TbwcCoTJrEX71gUn+U=", "t1ujJ5nVJj9AfU1+3rZz1WXUwhN6aFwMXRFPgENyezM=", "BLQ7pXxRmZnrxBGxvn9GmTTOvOYJedDzfmsiOfm30vs=", "GOqS3dsbFNNTUsx1Dsr6r9Sxs6fTKD0jY+IeMOqW/z0=", "nm0w77N5YoER1qq//Jx051siouZ7HeE+p5PG3CcPqqI=", "GlPOAWKyhv3vqQRbdvHU5+pewhIavyqY746CuE6fsUM=", "2Z8LH3gn6eSZTFj2UhkhouIY8xy9xvA/VM8qr8EgUBc=", "Kng5Y4Wy44juGPuXBX5eUtXfaDxzEv06CSochqnyZbY=", "ud6/MGuwoyYVgs16cEv3iGKvsP7PA75PAhkk2gDjFdU=", "zxHhVvjpP+nsHYtLb+bfntVsmszGNHqpIu61XQqi3Ug=", "K7lideJuoLdzL8CNkBcDJmiEYIWmtrEMNZckULc8WGw=", "OcYngIosF/ISgIX9vl5ebIyUNZYI71ErvwNKEW6oWrU=", "r+sPSCJQBAosZZS73plQTnakmIGRvz6RSDdB5uARh5w=", "GBGvB4nTS2N8MX9EKGoToWC5pAFYRqmcdyMOv0rFjTM=", "3NIfye6dpkIixj1y7AQCklLwJOXfhVFiJDlST9Ae4O8=", "hZfYsB/OfMUUkGk56Fno2WI02du5sQblrCOD2n599Lg=", "F+4f/b8YOm7Q840nEWBje2ODGiWEX+Q7AjuXdr5oMdQ=", "YVgLBcJ03ECQuShTp/CwoegXGF3olj3PVeiCgZK0Ppk=", "0mpdDfAConD5YpASd40kbIKYyZIPrMVa5lluiFxOCFE=", "CdpX8klLBwezRgj0I0KfluzLpACh/KeX6JC8M+ONsa0=", "ocEacE4Tids/N5qFf18HZ+A015Pwrzv1TYmMVHOF8RM=", "RaxXitqfXeAuGHs+mhtSGIa1A5KHyKGf7NLlHCrOhvQ=", "wlCCgK0YmS4re/NmQs6Gric3GutQnYEOFif+X6ZQRPA=", "vIltp3pZ8kjAd1I/dPXKTt0294pCsbQzPFVQ06PLpaE=", "LTUs4sfkfgl95ku1O3ExWMP+uVXgMbtCkRf9ZQ9i8Hs=", "4EJFGiP1uRfOBGRfvsklkyCc1BTo/gXfItWSMT8MUK0=", "rt3PSA/gOoxufpZZIYUfGvtoKhQIOlylettJvDJu0f8=", "GYh/npqvdIS4MrMlQqaTSBkWWmRtJPPYw+vd1hQKle8=", "NC9PfY9wifLSCr7pw2+yyjgEPvHjh729cKyUwvzRYck=", "zOcDa0zxd110+md7ZBsryoZEju2rVIw4mWu0JUT+RD8=", "7L3bdCwav/aTKLcGtRewNt0gt6GWZsRgX94onRYr02s=", "NE5cmqGwziDWyUurc8sMAMNik72vqexRl/MQX2y8BNo=", "Jv2k6uwfCdwsbvRxnKmY3FKS8n4XfuRWCZ4CmMszcIE=", "8s9xn3ZLUVsCbMhtrW3PfBHmHHZg0tliCXPv6+TCymw=", "eZ6X9rddXrJsGR9L5FSr6tl4iksQ26TM6HK6Ggg3JMY=", "/V6tDH6GEHSHqJQH8WL+z9mrrzNi4CO79IDH2zkzHoM=", "ArZ6ULkB67u8nUrAQlAplClKBaUnZWz4hgyRcrGit1g=", "rGdJGwQ6QFOTmbDmixwjd92I6JEzbwRDhbV15uMI7tI=", "oWbEigX2xg2ydloioEE53Y3D5jjO6Ay7RGNSwIIeKkY=", "ZJnfi909sdSql29rWb7Ig6+XNItGI62vGIRDdhGvkp8=", "H8VMW4jej+e/nIJazCzjwD55y1N5hShLTI7rw1eyiQg=", "UVGCK2tNKJbx0pGBni1Ij064ggYejcAniBNrbR1nA+k=", "sSASA7AX2U/yKgG5BlsCduR84Fwg3pZZgjGJYobuXKQ=", "lOBpmff5VMkDFYebzJiv11F0zxP9/mvRDcF+yFaIxgM=", "WDkYdgj/oZS+17Bq0//v+L/GInPhjCLMEcSgyNYZo90=", "0ChMEI0qytCVVMYZ26GfBrhdtUJ64A/s92zbpqU1nqM=", "XoOFpkUf9uIvJ0kEudl2YpoD9x01iN/rdTIi/So7Ky4=", "Y8mlfAgaXBg+c+sSd847l7/MkzK8JF/BvcGfJZQhc9I=", "ElDsZAtygEkwm2HkWbu5m/OeVDbNlHA57kHTbqUZ1rk=", "g6KxCNVtZXgBhs6oUSIQIwwHjSgPG31z/fXuOPqZ6Pg=", "txw1uMYUb3vCjIarEBFf6OX9Vc8K0RWH/g7fBZFpDs8=", "L+vsuaBhWFaj1N3hC7khS3xHac169JDiBk+90Id26xE=", "WbWrUiFe6wHLNdaGQ4OC/z9ytPtFaTS08uqkefBPthA=", "Wyvv+wNq77OkXbggjhv03vTVjrB8aCWMWI3C3AEYgaE=", "iJ5tyDWQJ+4CAs0NW5bkAcZ3diGkpH07HOaubCLWL9s=", "esPYA2akP0mJZNsjSQRetaD0kla/IgKuliVsI4hWx/Y=", "b4i9LsKTkw+O9zy/OkasYMPv3GptL+vgBw+USGQ4xiY=", "G7vcrsTczJDWyKp4iSH2FS5btltA+hB1Tx7AI66ECF8=", "LZIzlYcHhgdOFTFjJ0XB+1llAGzHiabyohIt9kw5I2k=", "X2NO2JUzRbHCHwXJHqd7kTdmeupSU7wl6Ajz+De1If8=", "2wLM0Y72mjvJ4dZ5t8tAsK+TDVCNQIgedeMsA+0Rd8s=", "tLLgHDjPUYqBOvYCykYcCW//jKPgcH2HBk39xtvhE4I=", "DWaZKwVr0HFsCSx+cdUzyagQeSc6dTrdbmmDd+itHoc=", "8mq8tVD6NU0m7lFTHAoGB7/6J5J/B9LjY7gQUMomMFM=", "Ay9jtmVrH7WSH9fiFlk/9KdcxJ1Ui6/c4eC0zPRuc+M=", "ZXbdJyyZeQp3WAIVG+ZuM4m3TURnmGpE37fwaOmEQQM=", "TqgoxplkxF4TpFwpsqK50khIfs34buBraZkTP40xf5k=", "RONzIqdnn+9CykH91Ik+Rh0RoNxy/MB8qrZ5bhmzFgw=", "QNsRav5wF42pw+L29AhkUq2DZwW/kOrHfqF7YLDcdTQ=", "YJ8iico7C9x1Ovr+Qw2L9JVetql7UbvJDbq8jKAxJeA=", "5gS00KEzEjx3ORCPs+oONB5/AKMSiNYJSFCaE2Bk0QM=", "vQXtwAuQS5a2SCNUc2oIZeMqCh+pZl7M8dnk7ccvQq8=", "nKN+SUWoH4uVWvvCth5EAJYlGV8VaGcutdszmdGIzQU="], "CachedAssets": {"FFyQVzNDXWVjh6jZpDY+UmTEGVYiOOOD5wCK2EW+yaI=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\bootstrapoverrides.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/bootstrapoverrides#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bmemeer8bj", "Integrity": "g2n7A1p7hDcqfdUKAR++fk9bC8rgv79noSOe4YAVR/Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrapoverrides.css", "FileLength": 4314, "LastWriteTime": "2025-07-18T11:35:16.8272351+00:00"}, "zkMc9rKMhZiF4aoG3ftguswyJpJosO4kjNbqsZn6Dmc=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\dashboard.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v17xt12bv5", "Integrity": "DZ34pAo5/z7nuB7JhYLqT2/czHCS6yyLCXt4Vd45B0Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashboard.css", "FileLength": 4110, "LastWriteTime": "2025-07-16T14:26:14.5292628+00:00"}, "UVGCK2tNKJbx0pGBni1Ij064ggYejcAniBNrbR1nA+k=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-16T10:10:12.613686+00:00"}, "sSASA7AX2U/yKgG5BlsCduR84Fwg3pZZgjGJYobuXKQ=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-16T10:10:12.6186897+00:00"}, "lOBpmff5VMkDFYebzJiv11F0zxP9/mvRDcF+yFaIxgM=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-16T10:10:12.6186897+00:00"}, "WDkYdgj/oZS+17Bq0//v+L/GInPhjCLMEcSgyNYZo90=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-16T10:10:12.6196901+00:00"}, "0ChMEI0qytCVVMYZ26GfBrhdtUJ64A/s92zbpqU1nqM=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-07-16T10:10:12.4545449+00:00"}, "XoOFpkUf9uIvJ0kEudl2YpoD9x01iN/rdTIi/So7Ky4=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-07-16T10:10:12.4545449+00:00"}, "Y8mlfAgaXBg+c+sSd847l7/MkzK8JF/BvcGfJZQhc9I=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-07-16T10:10:12.4555459+00:00"}, "ElDsZAtygEkwm2HkWbu5m/OeVDbNlHA57kHTbqUZ1rk=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-07-16T10:10:12.4565468+00:00"}, "g6KxCNVtZXgBhs6oUSIQIwwHjSgPG31z/fXuOPqZ6Pg=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-16T10:10:12.6156882+00:00"}, "txw1uMYUb3vCjIarEBFf6OX9Vc8K0RWH/g7fBZFpDs8=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-07-16T10:10:12.4515417+00:00"}, "L+vsuaBhWFaj1N3hC7khS3xHac169JDiBk+90Id26xE=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-07-16T10:10:12.4525421+00:00"}, "WbWrUiFe6wHLNdaGQ4OC/z9ytPtFaTS08uqkefBPthA=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-07-16T10:10:12.453544+00:00"}, "Wyvv+wNq77OkXbggjhv03vTVjrB8aCWMWI3C3AEYgaE=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-16T10:10:12.613686+00:00"}, "iJ5tyDWQJ+4CAs0NW5bkAcZ3diGkpH07HOaubCLWL9s=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\logo.png", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47qgg4w1ko", "Integrity": "oMjUcrj2w8RCoEboIsmstu/dymHDNwjUM0cBF3huHtc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\logo.png", "FileLength": 12987, "LastWriteTime": "2025-06-30T19:37:22+00:00"}, "97O3O/YMZJlAePwpfyaFgkvbNhyeLTyRlw6gq8ZyhSA=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\site.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u76nsz38ux", "Integrity": "xkjs9LGkZGzHzuNsvQ9xAp80c3zt+yNH+ymKDa9WGWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 1453, "LastWriteTime": "2025-07-18T11:35:16.8282357+00:00"}, "k5E0ekzSQWdcj1iDpguXnJhtIoxjtT8RXTQ7b/39GQM=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\header.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/header#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "30qyo1la85", "Integrity": "3buuO0G1gTL8dSE1l/uxG9hs+9MKmIVHxch4lUhrKJY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\header.css", "FileLength": 2261, "LastWriteTime": "2025-07-16T14:22:03.6803258+00:00"}, "LP03SHOk9ZloH+CwePzEd2w6M44X7kEPnEasUsNNAWc=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\main.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/main#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "93hh85iudv", "Integrity": "O7MNEFqkO4ag9WYS1Tl415qTpziL2jHu0zVg2Cj7co4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\main.css", "FileLength": 351, "LastWriteTime": "2025-07-16T14:23:26.824524+00:00"}, "y0B/wI1Z17U36xgsLFtJtPwRY4t+cD6xDtW3d39uLRk=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\css\\sidebar.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "css/sidebar#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "urz00uwuq2", "Integrity": "Gm0vDBskzPgjwsC1zu4ErHii74MK1xpS2dCx9LdVRgE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\sidebar.css", "FileLength": 3874, "LastWriteTime": "2025-07-16T14:24:42.8964285+00:00"}, "3l1Dymww9T3FVLbP2dz/UYPyFAdJpK0jBqq/pFYLv/M=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\js\\site.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w5okk3xrsw", "Integrity": "rPiiiU0/Qb2kJCPTWjqC1Y2VhcPLNX6awGTjoHe1qLA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 4419, "LastWriteTime": "2025-07-16T14:31:58.8169618+00:00"}, "fFsHcQ64wAXnKtcB1+xNn2iOoIfGQpwbo0A0eP8CHmo=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4qewknlhhe", "Integrity": "dA0sMMarH6f9nXmoG8n2UtTPgdML+FQEIQafdAdkmjY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 71861, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "OxhAyDFMX0BuaYQbLFH9mkFmZuUMtla+OJ8Jq1ElqcY=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lni73340tj", "Integrity": "z5b4JVUGDyuwhAgOyeNM8+zzTAVIg92Daa3arCt4/jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 210357, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "9XAKKBilL4toU1hPWY4wHvAzttC8St2yMTXlsQE3gzs=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dldhcrg7tj", "Integrity": "7whDaDTCHXXnonJID5WJ0M1IF+Hj7X7s/0weqm3E4w8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 53265, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "lQOLPha5CkxLaM5ciXMbCIia7TbwcCoTJrEX71gUn+U=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7mduziv6u0", "Integrity": "kWSDSU/LX9bceAFE+zgDkkiKRfLYCfsyoKMBd3yLNuE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 131395, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "t1ujJ5nVJj9AfU1+3rZz1WXUwhN6aFwMXRFPgENyezM=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k77ja6ojiw", "Integrity": "QA3I0wGVEgdWTezh1Ly8RkOUOSEuus87qkA5+ak8x6w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 71935, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "BLQ7pXxRmZnrxBGxvn9GmTTOvOYJedDzfmsiOfm30vs=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o2603f55wz", "Integrity": "wC3bx3+qYhUbuV2hgDKU6wvVy3wjREf0aCLllpfjPlU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 210361, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "GOqS3dsbFNNTUsx1Dsr6r9Sxs6fTKD0jY+IeMOqW/z0=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1h967pd9y7", "Integrity": "iw8sN76RnVWdkjjBaco0GXa4mAGBFgNwrSkUd7cU5m0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 53340, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "nm0w77N5YoER1qq//Jx051siouZ7HeE+p5PG3CcPqqI=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c9vlz3rxj1", "Integrity": "gI/LstkS7gHhVflvLvhmNBTfsmbPDZSw6CMHWPX2/u0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 131472, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "GlPOAWKyhv3vqQRbdvHU5+pewhIavyqY746CuE6fsUM=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aa7xbl0rhm", "Integrity": "3j2FAwc4alSE97seWjrnEM0rxxcETawYAkqrisCTdqM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 7965, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "2Z8LH3gn6eSZTFj2UhkhouIY8xy9xvA/VM8qr8EgUBc=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l3dvo8xkxk", "Integrity": "72O+Fh2PnPTlIiX1OVC+lj6RPhAMN4PnnC1EjQxojaY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 110875, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "Kng5Y4Wy44juGPuXBX5eUtXfaDxzEv06CSochqnyZbY=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f60t7moryz", "Integrity": "J2V6GX54gr5BlmDKAJhdMB/74IV4fNQmkvunz/u/gIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 6490, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "ud6/MGuwoyYVgs16cEv3iGKvsP7PA75PAhkk2gDjFdU=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2hu6j6r8fj", "Integrity": "xlpfHBt3lJhgUv25I9C2WSHo5Oe/adYvUELU9yZPgVs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 40331, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "zxHhVvjpP+nsHYtLb+bfntVsmszGNHqpIu61XQqi3Ug=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0ewczrgofc", "Integrity": "CIKwGoBmMnGolMkC26Ih1+OF6TkrtY8065mpt8fuYHc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 7958, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "K7lideJuoLdzL8CNkBcDJmiEYIWmtrEMNZckULc8WGw=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1g6wsxogud", "Integrity": "X9dM7nPbPZ3/8FRPnxpBHLbVAnmzu0s4MZvPTYQzWLY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 110888, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "OcYngIosF/ISgIX9vl5ebIyUNZYI71ErvwNKEW6oWrU=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gyj16cx8tl", "Integrity": "SKHul1OeVlV9lIhBRDvET4C4yTBPJ2rWZJYpXU23QU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 6562, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "r+sPSCJQBAosZZS73plQTnakmIGRvz6RSDdB5uARh5w=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6k0x1vcin3", "Integrity": "Fj4DFKGbMgZSyXiCEw3IUd/btiwkRFs0tsOoLWUbw1Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 48693, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "GBGvB4nTS2N8MX9EKGoToWC5pAFYRqmcdyMOv0rFjTM=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "avhg9diubf", "Integrity": "dlvexF1Qkd5AgNMZNNeeLThKtlyK0iwZzcnVUavT4dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 76347, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "3NIfye6dpkIixj1y7AQCklLwJOXfhVFiJDlST9Ae4O8=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nmg369ex7u", "Integrity": "7yBkd/s6BIpUAlTETbNsyNQaYZLLGI7L6SgeVVmo3TQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 212450, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "hZfYsB/OfMUUkGk56Fno2WI02du5sQblrCOD2n599Lg=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3lukdf9ezp", "Integrity": "KU++V32xsw6TH1GuL0o7T9CJLiWy6nE2d+qXkuLPslI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 58266, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "F+4f/b8YOm7Q840nEWBje2ODGiWEX+Q7AjuXdr5oMdQ=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hlpl0prczm", "Integrity": "1Shm7R14oQwwiV1QsDWm+7oE8QkYZ6Pe9arxK8Ffn6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 131956, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "YVgLBcJ03ECQuShTp/CwoegXGF3olj3PVeiCgZK0Ppk=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "p13uj7zzzw", "Integrity": "EzakB0ov6XPobdt8Q3KdM+z7R8+gP35c8wKoCarkKaI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 76214, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "0mpdDfAConD5YpASd40kbIKYyZIPrMVa5lluiFxOCFE=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rat98tutyt", "Integrity": "k7hPEPRS175eEjSY2Nippzm1vvZWG8GhZ3Rve/r5GS0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 212393, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "CdpX8klLBwezRgj0I0KfluzLpACh/KeX6JC8M+ONsa0=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwuaox3pd4", "Integrity": "7GY8mLhLIsg9XT7KutdNpHfVKneAGfl6YSUY0iDyj6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 58194, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "ocEacE4Tids/N5qFf18HZ+A015Pwrzv1TYmMVHOF8RM=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j526q8vwd3", "Integrity": "Z7pXbr1opJxXcP1W7qJUzeZ6dkAPoWnQecYRnNNs7QI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 131791, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "RaxXitqfXeAuGHs+mhtSGIa1A5KHyKGf7NLlHCrOhvQ=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2w652sptew", "Integrity": "IrRkRhwdO2IcP2+1tni2UxqwpwTQ4b0Hjd03G4dHPbA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 237950, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "wlCCgK0YmS4re/NmQs6Gric3GutQnYEOFif+X6ZQRPA=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yk6841fnnu", "Integrity": "rlQw1WO6mXXYVDz7AGUOKDsYwZyWA0YahGDyNyrwPyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 608300, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "vIltp3pZ8kjAd1I/dPXKTt0294pCsbQzPFVQ06PLpaE=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "00o7me0wi0", "Integrity": "wLz3iY/cO4e6vKZ4zRmo4+9XDpMcgKOvv/zEU3OMlRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 194901, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "LTUs4sfkfgl95ku1O3ExWMP+uVXgMbtCkRf9ZQ9i8Hs=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tlr56zwpn9", "Integrity": "/3JMpzrFCpORhREpzUUcmOjDAVoo/4Ufl9rSR7BSpXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 522639, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "4EJFGiP1uRfOBGRfvsklkyCc1BTo/gXfItWSMT8MUK0=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9065ftxq2z", "Integrity": "V7+lvNgNoUXMaX84L82od+vYO9y9HLLVTFs1/nIOawg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 237528, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "rt3PSA/gOoxufpZZIYUfGvtoKhQIOlylettJvDJu0f8=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "slkxxxvi35", "Integrity": "cMQQvs/vpnihjbAzam82xiSDoMs0U/hxBs/EGtkps5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 608144, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "GYh/npqvdIS4MrMlQqaTSBkWWmRtJPPYw+vd1hQKle8=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tf8d8chiq4", "Integrity": "+bNAolFvt6YB2LgXdEQJhKQUw4T0XbrBxLf0lH4NYDo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 195007, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "NC9PfY9wifLSCr7pw2+yyjgEPvHjh729cKyUwvzRYck=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lbdsg3nnm3", "Integrity": "j6HtokF3Fu+wzaVy6wlgWacCVQGfcwoTL6lBOyUS/k4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 767483, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "zOcDa0zxd110+md7ZBsryoZEju2rVIw4mWu0JUT+RD8=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kpvfgawzjd", "Integrity": "SIYbLg8Kyb12W8RgxT7zwNAeUMfrfld1XpKbc/0Q1hE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207989, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "7L3bdCwav/aTKLcGtRewNt0gt6GWZsRgX94onRYr02s=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qm38hbak0o", "Integrity": "nt6dM/isl7bjessuHdkV9PU4lygDbdsJu3BlPeRbyAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 451770, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "NE5cmqGwziDWyUurc8sMAMNik72vqexRl/MQX2y8BNo=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pjhbf5ty2q", "Integrity": "lSABj6XYH05NydBq+1dvkMu6uiCc/MbLYOFGRkf3iQs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80420, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "Jv2k6uwfCdwsbvRxnKmY3FKS8n4XfuRWCZ4CmMszcIE=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "llx8tuhx2q", "Integrity": "GSH9nQGDQNh9D2nORvaKUuolz5n+lrmOHedM/P+WVZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 333078, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "8s9xn3ZLUVsCbMhtrW3PfBHmHHZg0tliCXPv6+TCymw=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "axg36zpfrh", "Integrity": "bvotsoWb+w503qAye1hpcxn4aAxCrjnUudT3kXdZB14=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 136215, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "eZ6X9rddXrJsGR9L5FSr6tl4iksQ26TM6HK6Ggg3JMY=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o6r5icn62k", "Integrity": "IPVMyDWoTT+wyxMWqyZcAtByCdSYtiPZhH0lBrbCjP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 308207, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "/V6tDH6GEHSHqJQH8WL+z9mrrzNi4CO79IDH2zkzHoM=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uotu6b0c8w", "Integrity": "MjwoGBhLjqbgaw3cyYaBigjnHsRrLa8zyw9vr07xkKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73978, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "ArZ6ULkB67u8nUrAQlAplClKBaUnZWz4hgyRcrGit1g=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1d4puk7gs", "Integrity": "DAwUlccSFtUDppCbLxTVrMFHr+x0gs0hLsigIs/BTOI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 221179, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "rGdJGwQ6QFOTmbDmixwjd92I6JEzbwRDhbV15uMI7tI=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ib7told66d", "Integrity": "TqbemDLI4XxVoyOpffv+FTL5oPnnT0a/qWLJy2/ANeg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145543, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "oWbEigX2xg2ydloioEE53Y3D5jjO6Ay7RGNSwIIeKkY=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s9kdte20bp", "Integrity": "4PzS+joOjuZh3PmGu53C0hw+22N6yM8MIofFXsMeYvo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 309348, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "ZJnfi909sdSql29rWb7Ig6+XNItGI62vGIRDdhGvkp8=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtzwxpr1me", "Integrity": "m81NDyncZVbr7v9E6qCWXwx/cwjuWDlHCMzi9pjMobA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60404, "LastWriteTime": "2023-03-09T09:11:54+00:00"}, "H8VMW4jej+e/nIJazCzjwD55y1N5hShLTI7rw1eyiQg=": {"Identity": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Life", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Dropbox\\Projects\\Life\\src\\Life\\Life\\wwwroot\\", "BasePath": "_content/Life", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lxnhmosdkt", "Integrity": "lzaJGZxMdBemIVMIbmmcIuJsWsH+uvcRXO2rj0IjDhc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 216913, "LastWriteTime": "2023-03-09T09:11:54+00:00"}}, "CachedCopyCandidates": {}}